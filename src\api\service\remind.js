import request from '@/utils/request'


// 数据催收查询
export function detailDataUrge(data) {
    return request({
        url: '/dataclean/dataurge/detailDataUrge',
        method: 'post',
        data: data
    })
}

// 数据催收更新
export function updateDataUrge(data) {
    return request({
        url: '/dataclean/dataurge/updateDataUrge',
        method: 'post',
        data: data
    })
}

// 异常提醒详情
export function detailDataYctx(data) {
    return request({
        url: '/dataclean/dataurge/detailDataYctx',
        method: 'post',
        data: data
    })
}

// 异常提醒更新
export function updateDataYctx(data) {
    return request({
        url: '/dataclean/dataurge/updateDataYctx',
        method: 'post',
        data: data
    })
}

// 服务短信查询
export function detailSms(data) {
    return request({
        url: '/service/sms/detailSms',
        method: 'post',
        data: data
    })
}

// 服务短信更新
export function updateSms(data) {
    return request({
        url: '/service/sms/updateSms',
        method: 'post',
        data: data
    })
}