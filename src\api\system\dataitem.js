import request from '@/utils/request'

// 查询数据项配置列表
export function listDataitem(data) {
  return request({
    url: '/system/dataitem/list',
    method: 'post',
    data: data
  })
}

// 查询数据项配置详细
export function getDataitem(itemId) {
  return request({
    url: '/system/dataitem/info',
    method: 'post',
    data: {"itemId": itemId}
  })
}

// 新增数据项配置
export function addDataitem(data) {
  return request({
    url: '/system/dataitem',
    method: 'post',
    data: data
  })
}

// 修改数据项配置
export function updateDataitem(data) {
  return request({
    url: '/system/dataitem',
    method: 'put',
    data: data
  })
}

// 删除数据项配置
export function delDataitem(itemId) {
  return request({
    url: '/system/dataitem/del',
    method: 'post',
    data: {"itemIds": itemId}
  })
}

// 导入列
export function importItem(data) {
  return request({
    url: '/system/dataitem/importItem',
    method: 'post',
    data: data
  })
}

// 获取主体信息基本表字段
export function getbaseinfodataitemlist(data) {
  return request({
    url: '/system/dataitem/getbaseinfodataitemlist',
    method: 'post',
    data: data
  })
}
