import request from '@/utils/request'

// 查询指标管理列表
export function listNorm(data) {
  return request({
    url: '/system/norm/list',
    method: 'post',
    data: data
  })
}

export function queryUsedTable(data) {
  return request({
    url: '/system/datatable/queryUsedTables',
    method: 'post',
    data: data
  })
}

export function queryUsedItem(data) {
  return request({
    url: '/system/dataitem/queryUsedItems',
    method: 'post',
    data: data
  })
}

// 查询指标管理详细
export function getNorm(normId) {
  return request({
    url: '/system/norm/info',
    method: 'post',
    data:{"normId":normId}
  })
}

// 检验计算方法内容
export function checkCalcMethod(calcMethod) {
  return request({
    url: '/system/norm/checkcalcmethod',
    method: 'post',
    data:{"calcMethod":calcMethod}
  })
}

// 新增指标管理
export function addNorm(data) {
  return request({
    url: '/system/norm',
    method: 'post',
    data: data
  })
}

// 修改指标管理
export function updateNorm(data) {
  return request({
    url: '/system/norm',
    method: 'put',
    data: data
  })
}

// 删除指标管理
export function delNorm(normId) {
  return request({
    url: '/system/norm/del',
    method: 'post',
    data:{"normIds":normId}
  })
}
