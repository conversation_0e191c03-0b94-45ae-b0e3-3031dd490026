import request from '@/utils/request'


// 表单模板列表
export function modelLists(data) {
    return request({
        url: '/smart/model/modelLists',
        method: 'post',
        data: data
    })
}

// 新增表单模板
export function modelAdd(data) {
    return request({
        url: '/smart/model/add',
        method: 'post',
        data: data
    })
}

// 更新模板
export function modelUpdate(data) {
    return request({
        url: '/smart/model/update',
        method: 'post',
        data: data
    })
}

// 删除模板
export function modelDelete(data) {
    return request({
        url: '/smart/model/delete',
        method: 'post',
        data: data
    })
}

// 设置启用模板
export function setDefaultModel(data) {
    return request({
        url: '/smart/model/setDefaultModel',
        method: 'post',
        data: data
    })
}

//获取首页法人和自然人的服务
export function getBussList(query) {
    return request({
        url: "/smart/buss/getBussList",
        method: 'post',
        data: query
    });
}

//获取服务表单模板
export function getBussModel(query) {
    return request({
        url: '/smart/model/getBussModel',
        method: 'post',
        data: query
    })
}