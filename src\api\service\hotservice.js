import request from '@/utils/request'


// 热门服务列表
export function getBussHotList(data) {
    return request({
        url: '/smart/buss/getBussHotList',
        method: 'post',
        data: data
    })
}

// 热门服务新增
export function addBussHot(data) {
    return request({
        url: '/smart/buss/addBussHot',
        method: 'post',
        data: data
    })
}

// 热门服务修改
export function updateBussHot(data) {
    return request({
        url: '/smart/buss/updateBussHot',
        method: 'post',
        data: data
    })
}

// 热门服务删除
export function deleteBussHot(data) {
    return request({
        url: '/smart/buss/deleteBussHot',
        method: 'post',
        data: data
    })
}

// 服务配置列表
export function getBussConfigList(data) {
    return request({
        url: '/smart/buss/getBussConfigList',
        method: 'post',
        data: data
    })
}

// 服务配置列表新增
export function addBussConfig(data) {
    return request({
        url: '/smart/buss/addBussConfig',
        method: 'post',
        data: data
    })
}

// 服务配置列表详情
export function getBussConfig(data) {
    return request({
        url: '/smart/buss/getBussConfig',
        method: 'post',
        data: data
    })
}

// 服务配置列表修改
export function updateBussConfig(data) {
    return request({
        url: '/smart/buss/updateBussConfig',
        method: 'post',
        data: data
    })
}