import request from '@/utils/request'

// 查询公告列表
export function listNotice(query) {
    return request({
        url: '/system/notice/list',
        method: 'get',
        params: query
    })
}

// 查询公告详细
export function getNotice(noticeId) {
    return request({
        url: '/system/notice/' + noticeId,
        method: 'get'
    })
}

// 新增公告
export function addNotice(data) {
    return request({
        url: '/system/notice',
        method: 'post',
        data: data
    })
}

// 修改公告
export function updateNotice(data) {
    return request({
        url: '/system/notice',
        method: 'put',
        data: data
    })
}

// 删除公告
export function delNotice(noticeId) {
    return request({
        url: '/system/notice/' + noticeId,
        method: 'delete'
    })
}
// 栏目导航
export function getNav(data) {
    return request({
        url: '/system/menu/getRouters',
        method: 'get'
    })
}

// 代办任务
export function getDbInfo(data) {
    return request({
        url: '/service/dbEvent/getDbInfo',
        method: 'get'
    })
}

// 督办数据信息
export function getDeptsDbInfo(data) {
    return request({
        url: '/service/dbEvent/getDeptsDbInfo',
        method: 'get',
        timeout: 60000
    })
}

// 督办部门信息
export function getDeptTree(data) {
    return request({
        url: '/service/dbEvent/getDeptTree',
        method: 'get'
    })
}
// 数据采集首页数据统计
export function homePage(data) {
    return request({
        url: '/dataclean/statistics/homePage',
        method: 'post',
        data: data
    })
}

// 通知公告
export function getLast(data) {
    return request({
        url: '/system/notice/getLast',
        method: 'post',
        data: data
    })
}