import request from '@/utils/request'
import { encode } from 'js-base64';

const client_id = 'web'
const client_secret = '123456'
const scope = 'server'

// 登录方法
export function login(username, password, code, uuid) {
    var sm4 = new SM4Util();
    let encryptPwd = sm4.encryptData_CBC(password);

    return request({
        url: '/auth/login',
        method: 'post',
        data: { username, password: encryptPwd, code, uuid }
    })
}
// USB-KEY 登录
export function USB_KEY_login(deptName) {
    // console.log('USB-KEY 登录==deptName')
    // console.log(deptName)
    return request({
        url: '/system/sysUserDec/get/tokens',
        method: 'post',
        data: { deptName}
    })
}

// 刷新方法
export function refreshToken() {
    return request({
        url: '/auth/refresh',
        method: 'post'
    })
}

// 获取用户详细信息
export function getInfo() {
    return request({
        url: '/system/user/getInfo',
        method: 'get'
    })
}

// 退出方法
export function logout() {
    return request({
        url: '/auth/logout',
        method: 'delete'
    })
}

// 获取验证码
export function getCodeImg() {
    return request({
        url: '/code',
        method: 'get'
    })
}

// ca
// 获取随机数
export function caGetGenRandom() {
  return request({
    url: "/system/bjca/genRandom",
    method: "post",
  });
}

// 原文验签
export function caVerifyDataSign(data) {
  return request({
    url: "/system/bjca/verifyDataSign",
    method: "post",
    data
  });
}

// 根据证书验证免密登录;
export function caTokens(certificate) {
  return request({
    url: "/system/bjca/tokens",
    method: "post",
    data: { certificate },
  });
}
