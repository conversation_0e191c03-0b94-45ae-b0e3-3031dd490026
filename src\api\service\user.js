import request from '@/utils/request'


// 用户管理
export function listUser(data) {
    return request({
        url: '/system/sysUserDec/list',
        method: 'post',
        data: data
    })
}
// 用户详情
export function getSysUserInfo(id) {
    return request({
        url: '/system/sysUserDec/infoById',
        method: 'post',
        data: { userId: id }
    })
}
// 用户修改
export function updateSysUserInfo(query) {
    return request({
        url: '/system/sysUserDec/edit',
        method: 'post',
        data: query
    })
}