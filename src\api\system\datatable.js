import request from '@/utils/request'

// 查询数据配置列表
export function listDatatable(data) {
  return request({
    url: '/system/datatable/list',
    method: 'post',
    data: data
  })
}
// 查询数据配置列表
export function listOtherDatatable(data) {
  return request({
    url: '/system/datatable/otherlist',
    method: 'post',
    data: data
  })
}

// 查询数据配置详细
export function getDatatable(tableId) {
  return request({
    url: '/system/datatable/info',
    method: 'post',
    data:{"tableId":tableId}
  })
}


// 查询db数据库列表
export function listDbTable(data) {
  return request({
    url: '/system/datatable/db/list',
    method: 'post',
    data: data
  })
}

// 查询db数据库表中字段
export function listDbTableColumn(tableName, columnName, columnComment) {
  return request({
    url: '/system/datatable/column',
    method: 'post',
    data:{tableName, columnName, columnComment}
  })
}

// 新增数据配置
export function addDatatable(data) {
  return request({
    url: '/system/datatable',
    method: 'post',
    data: data
  })
}

// 修改数据配置
export function updateDatatable(data) {
  return request({
    url: '/system/datatable',
    method: 'put',
    data: data
  })
}

// 删除数据配置
export function delDatatable(tableId) {
  return request({
    url: '/system/datatable/del',
    method: 'post',
    data:{"tableIds":tableId}
  })
}
