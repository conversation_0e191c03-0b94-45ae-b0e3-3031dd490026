import request from '@/utils/request'


// 短信服务配置列表
export function configLists(data) {
  return request({
    url: '/sms/sendMes/configLists',
    method: 'post',
    data: data
  })
}

// 新增短信服务配置
export function addConfig(data) {
  return request({
    url: '/sms/sendMes/addConfig',
    method: 'post',
    data: data
  })
}

// 删除短信服务配置
export function deleteConfig(query) {
  return request({
    url: '/sms/sendMes/deleteConfig',
    method: 'post',
    data: query
  })
}

// 修改短信服务配置
export function updateConfig(query) {
  return request({
    url: '/sms/sendMes/updateConfig',
    method: 'post',
    data: query
  })
}

// 设置默认服务配置
export function defaultConfig(query) {
  return request({
    url: '/sms/sendMes/defaultConfig',
    method: 'post',
    data: query
  })
}


// 查询模板列表
export function tempLists(data) {
  return request({
    url: '/sms/sendMes/tempLists',
    method: 'post',
    data: data
  })
}

// 新增短信模板
export function insertTemp(data) {
  return request({
    url: '/sms/sendMes/insertTemp',
    method: 'post',
    data: data
  })
}

// 删除短信模板
export function deleteTemp(query) {
  return request({
    url: '/sms/sendMes/deleteTemp',
    method: 'post',
    data: query
  })
}

// 查询短信发送记录
export function recordLists(data) {
  return request({
    url: '/sms/sendMes/recordLists',
    method: 'post',
    data: data
  })
}
