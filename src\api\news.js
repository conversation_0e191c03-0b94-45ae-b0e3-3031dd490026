import request from '@/utils/request'
// 列表查询
export function newsList(data) {
    return request({
        url: '/system/dynamicnews/querynews',
        method: 'get',
        params: data
    })
}
// 删除新闻
export function delnews(data) {
    return request({
        url: '/system/dynamicnews/delDynamicnews',
        method: 'post',
        data: data
    })
}
// 发布新闻
export function addnews(data) {
    return request({
        url: '/system/dynamicnews/addDynamicnews',
        method: 'post',
        data: data
    })
}
// 新闻详情
export function listdetail(data) {
    return request({
        url: '/system/dynamicnews/queryNewsDetail',
        method: 'get',
        params: data
    })
}
// 修改新闻
export function editnews(data) {
    return request({
        url: '/system/dynamicnews/updateDynamicnews',
        method: 'post',
        data: data
    })
}
// 是否停用
export function isTYtnews(data) {
    return request({
        url: '/system/dynamicnews/updateDynamicnewsStatus',
        method: 'post',
        data: data
    })
}
// 下载附件
export function downfile(data) {
    return request({
        url: '/system/common/oss/jdlh/' + data,
        method: 'get',
        // params: data
    })
}
// 删除附件
export function delfile(filename) {
    return request({
      url: '/system/common/oss/jdlh/' + filename,
      method: 'delete'
    })
  }